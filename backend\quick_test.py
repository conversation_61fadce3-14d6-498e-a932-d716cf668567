"""
Quick test script for Gemini API - Simple and fast verification
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def quick_gemini_test():
    """Quick test of Gemini API connection"""
    print("🚀 Quick Gemini API Test")
    print("-" * 30)
    
    # Check API key
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ GEMINI_API_KEY not found!")
        print("💡 Create .env file with: GEMINI_API_KEY=your_api_key_here")
        return False
    
    print(f"✅ API Key: {api_key[:10]}...{api_key[-4:]}")
    
    # Test import
    try:
        import google.generativeai as genai
        print("✅ google-generativeai imported successfully")
    except ImportError:
        print("❌ google-generativeai not installed!")
        print("💡 Run: pip install google-generativeai")
        return False
    
    # Test connection
    try:
        print("🌐 Testing connection...")
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-2.0-flash-lite')
        
        response = model.generate_content("Translate 'Xin chào' to English")
        
        if response and response.text:
            print(f"✅ Connection OK! Translation: {response.text.strip()}")
            return True
        else:
            print("❌ Empty response from API")
            return False
            
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

if __name__ == "__main__":
    success = quick_gemini_test()
    if success:
        print("\n🎉 Gemini API is ready!")
        print("💡 Run 'python test_api.py' for comprehensive tests")
    else:
        print("\n❌ Please fix the issues above before proceeding") 